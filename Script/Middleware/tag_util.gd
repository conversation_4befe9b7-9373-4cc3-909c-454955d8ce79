class_name TagUtil

## Tag工具类 - 处理物品标签的解析、编码和效果处理
## 作为中间件层，不直接操作UI或统计数据

# ================================
# Tag 处理字典和注册表
# ================================

static var _tag_handlers: Dictionary = {}
static var _initialized: bool = false

# ================================
# Tag 解析函数
# ================================

## 将标签字符串解析为字典
## 例如: "use:1,hp:50,slot:weapon" -> {"use": 1, "hp": 50, "slot": "weapon"}
static func parse(tags_string: String) -> Dictionary:
	var result: Dictionary = {}
	if tags_string.is_empty():
		return result
	
	var pairs = tags_string.split(",")
	for pair in pairs:
		var kv = pair.split(":")
		if kv.size() == 2:
			var key = kv[0].strip_edges()
			var value_str = kv[1].strip_edges()
			
			# 尝试转换为数值，否则保持字符串
			if value_str.is_valid_int():
				result[key] = int(value_str)
			elif value_str.is_valid_float():
				result[key] = float(value_str)
			else:
				result[key] = value_str
	
	return result

## 将标签字典编码为字符串
## 例如: {"use": 1, "hp": 50, "slot": "weapon"} -> "use:1,hp:50,slot:weapon"
static func encode(tags_dict: Dictionary) -> String:
	var pairs: Array[String] = []
	for key in tags_dict:
		pairs.append("%s:%s" % [key, str(tags_dict[key])])
	return ",".join(pairs)

# ================================
# Tag 查询工具函数
# ================================

## 获取标签整数值 - 支持ItemTemplate和ItemInstance
static func tagi(item, key: String, default_value: int = 0) -> int:
	var item_tags = _get_item_tags(item)
	if not item_tags:
		return default_value
	return item_tags.get(key, default_value)

## 获取标签浮点值 - 支持ItemTemplate和ItemInstance
static func tagf(item, key: String, default_value: float = 0.0) -> float:
	var item_tags = _get_item_tags(item)
	if not item_tags:
		return default_value
	return item_tags.get(key, default_value)

## 获取标签字符串值 - 支持ItemTemplate和ItemInstance
static func tags(item, key: String, default_value: String = "") -> String:
	var item_tags = _get_item_tags(item)
	if not item_tags:
		return default_value
	return item_tags.get(key, default_value)

## 内部函数：统一获取标签字典，支持新架构
static func _get_item_tags(item) -> Dictionary:
	if not item:
		return {}

	# 如果是ItemInstance，获取模板的tags
	if item.has_method("get_template") or item.get("template"):
		var template = item.template if item.has("template") else item.get_template()
		if template and template.has("tags"):
			return _parse_tags_if_needed(template.tags)

	# 如果是ItemTemplate或旧的Item类
	elif item.has("tags"):
		return _parse_tags_if_needed(item.tags)

	return {}

## 内部函数：解析标签（如果需要）
static func _parse_tags_if_needed(tags_data) -> Dictionary:
	if typeof(tags_data) == TYPE_STRING:
		return parse(tags_data)
	elif typeof(tags_data) == TYPE_DICTIONARY:
		return tags_data
	return {}

## 检查物品是否可使用 - 支持ItemInstance的动态使用次数
static func is_usable(item) -> bool:
	# 如果是ItemInstance，检查实例的剩余使用次数
	if item.has_method("is_usable"):
		return item.is_usable()

	# 如果是模板，检查模板的use标签
	var use_value = tagi(item, "use", 0)
	return use_value > 0 or use_value == -1

## 检查物品是否可装备（有slot标签）
static func is_equipable(item) -> bool:
	var slot_value = tags(item, "slot", "")
	return not slot_value.is_empty()

## 获取装备槽位类型
static func get_equipment_slot(item) -> int:
	var slot_str = tags(item, "slot", "")
	match slot_str.to_lower():
		"weapon":
			return InventoryManager.EquipmentSlot.WEAPON
		"armor":
			return InventoryManager.EquipmentSlot.ARMOR
		"accessory":
			return InventoryManager.EquipmentSlot.ACCESSORY
		"shoes":
			return InventoryManager.EquipmentSlot.SHOES
		"helmet":
			return InventoryManager.EquipmentSlot.HELMET
		_:
			return -1  # 无效槽位

# ================================
# 使用次数管理 - 重构为支持ItemInstance
# ================================

## 消耗物品使用次数 - 只对ItemInstance有效
static func consume_use(item) -> bool:
	# 如果是ItemInstance，调用其consume_use方法
	if item.has_method("consume_use"):
		return item.consume_use()

	# 如果是模板，不应该修改，返回false
	print("[TagUtil] Warning: Trying to consume use on template item: %s" % (item.name if item.has("name") else "Unknown"))
	return false

## 检查剩余使用次数 - 支持ItemInstance和模板
static func get_remaining_uses(item) -> int:
	# 如果是ItemInstance，获取实例的剩余使用次数
	if item.has_method("get_remaining_uses"):
		return item.get_remaining_uses()

	# 如果是模板，返回模板定义的使用次数
	return tagi(item, "use", 0)

# ================================
# 效果处理器注册系统
# ================================

## 注册标签处理器
## handler 应该是一个接受 (stat_service, item, value) 参数的可调用对象
static func register_handler(tag_key: String, handler: Callable):
	_ensure_initialized()
	_tag_handlers[tag_key] = handler
	print("[TagUtil] Registered handler for tag: %s" % tag_key)

## 移除标签处理器
static func unregister_handler(tag_key: String):
	_ensure_initialized()
	if _tag_handlers.has(tag_key):
		_tag_handlers.erase(tag_key)
		print("[TagUtil] Unregistered handler for tag: %s" % tag_key)

## 应用物品使用效果
static func apply_use_effects(stat_service, item):
	if not item or not item.tags:
		return

	var my_tags = _get_item_tags(item)
	
	# 遍历所有标签，查找已注册的处理器
	for key in my_tags:
		if _tag_handlers.has(key):
			var handler = _tag_handlers[key]
			var value = my_tags[key]
			if handler.is_valid():
				handler.call(stat_service, item, value)
			else:
				print("[TagUtil] Error calling handler for tag %s: %s" % [key, get_stack()])

## 应用装备效果（装备时apply=true，卸下时apply=false）
static func apply_equipment_effects(stat_service, item, apply: bool = true):
	if not item or not item.tags:
		return

	var my_tags = _get_item_tags(item)
	
	# 装备效果通常是数值加成，需要根据apply参数决定加还是减
	var multiplier = 1 if apply else -1
	
	# 只处理数值类型的标签（排除slot等非数值标签）
	var stat_tags = ["atk", "def", "hp", "mp", "spd", "attack", "defense", "speed", "max_hp", "max_mp"]
	
	for key in my_tags:
		if key in stat_tags and _tag_handlers.has(key):
			var handler = _tag_handlers[key]
			var value = my_tags[key]
			if typeof(value) == TYPE_INT or typeof(value) == TYPE_FLOAT:
				if handler.is_valid():
					handler.call(stat_service, item, value * multiplier)
				else:
					print("[TagUtil] Error calling equipment handler for tag %s: %s" % [key, get_stack()])

# ================================
# 内置处理器初始化
# ================================

static func _ensure_initialized():
	if _initialized:
		return
	
	_initialized = true
	print("[TagUtil] Initialized tag processing system")

## 注册默认的标签处理器（由Service层调用）
static func register_default_handlers(stat_service):
	_ensure_initialized()
	
	# 生命值恢复
	register_handler("hp", func(service, _item, value):
		if service.has_method("heal"):
			service.heal(value)
		else:
			print("[TagUtil] HP effect: %+d" % value)
	)
	
	# 魔法值恢复
	register_handler("mp", func(service, _item, value):
		if service.has_method("restore_mana"):
			service.restore_mana(value)
		else:
			print("[TagUtil] MP effect: %+d" % value)
	)
	
	# 攻击力加成
	register_handler("atk", func(service, _item, value):
		if service.has_method("modify_stat"):
			service.modify_stat("attack", value)
		else:
			print("[TagUtil] Attack effect: %+d" % value)
	)
	
	register_handler("attack", func(service, _item, value):
		if service.has_method("modify_stat"):
			service.modify_stat("attack", value)
		else:
			print("[TagUtil] Attack effect: %+d" % value)
	)
	
	# 防御力加成
	register_handler("def", func(service, _item, value):
		if service.has_method("modify_stat"):
			service.modify_stat("defense", value)
		else:
			print("[TagUtil] Defense effect: %+d" % value)
	)
	
	register_handler("defense", func(service, _item, value):
		if service.has_method("modify_stat"):
			service.modify_stat("defense", value)
		else:
			print("[TagUtil] Defense effect: %+d" % value)
	)
	
	# 速度加成
	register_handler("spd", func(service, _item, value):
		if service.has_method("modify_stat"):
			service.modify_stat("speed", value)
		else:
			print("[TagUtil] Speed effect: %+d" % value)
	)
	
	register_handler("speed", func(service, _item, value):
		if service.has_method("modify_stat"):
			service.modify_stat("speed", value)
		else:
			print("[TagUtil] Speed effect: %+d" % value)
	)
	
	# 最大生命值加成
	register_handler("max_hp", func(service, _item, value):
		if service.has_method("modify_stat"):
			service.modify_stat("max_hp", value)
		else:
			print("[TagUtil] Max HP effect: %+d" % value)
	)
	
	# 最大魔法值加成
	register_handler("max_mp", func(service, _item, value):
		if service.has_method("modify_stat"):
			service.modify_stat("max_mp", value)
		else:
			print("[TagUtil] Max MP effect: %+d" % value)
	)
	
	print("[TagUtil] Default handlers registered")

# ================================
# 调试和工具函数
# ================================

## 打印物品的所有标签信息
static func debug_print_item_tags(item):
	if not item:
		print("[TagUtil] Item is null")
		return
	
	print("[TagUtil] Item: %s" % item.name if item.name else "Unknown")

	if not item.tags:
		print("[TagUtil] No tags found")
		return
	
	var my_tags = item.tags if item.tags else {}
	if typeof(my_tags) == TYPE_STRING:
		print("[TagUtil] Raw tags string: '%s'" % my_tags)
		my_tags = parse(my_tags)
	
	print("[TagUtil] Parsed tags:")
	for key in my_tags:
		print("  %s: %s (%s)" % [key, my_tags[key], type_string(typeof(my_tags[key]))])
	
	print("[TagUtil] Usable: %s" % is_usable(item))
	print("[TagUtil] Equipable: %s" % is_equipable(item))
	if is_equipable(item):
		print("[TagUtil] Equipment slot: %d" % get_equipment_slot(item))

## 获取所有已注册的处理器列表
static func get_registered_handlers() -> Array:
	_ensure_initialized()
	return _tag_handlers.keys()

## 清理所有处理器（主要用于测试）
static func clear_all_handlers():
	_ensure_initialized()
	_tag_handlers.clear()
	print("[TagUtil] All handlers cleared")
